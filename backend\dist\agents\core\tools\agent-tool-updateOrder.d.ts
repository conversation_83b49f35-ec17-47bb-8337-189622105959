export declare function updateOrderTool(params: {
    orderNumber: string;
    customerName?: string;
    customerEmail?: string;
    customerPhone?: string;
    itemsToAdd?: Array<{
        productId: string | number | bigint;
        productName?: string;
        quantity: number;
        taxAmount?: number;
    }>;
    itemsToRemove?: Array<{
        productId: string | number | bigint;
        quantity?: number;
    }>;
    removeAllItems?: boolean;
    replaceQuantities?: boolean;
    useTax?: boolean;
    taxRate?: number;
    priority?: 'low' | 'normal' | 'high' | 'urgent';
    status?: 'draft' | 'pending' | 'confirmed' | 'processing' | 'shipped' | 'delivered' | 'cancelled';
    expectedDeliveryDate?: string;
    preferredDeliveryLocation?: string;
}, db: any, conversationUuid: string): Promise<{
    message: string;
    orderId: any;
    orderNumber: any;
    total: any;
    status: any;
    items: any[];
    itemsAdded: number;
    itemsRemoved: string | number;
    totalItems: number;
}>;

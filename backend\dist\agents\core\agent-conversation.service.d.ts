import { DataSource } from 'typeorm';
import { AgentRuntimeCallbacks, GenerateMessageInput, ProcessUserMessageInput } from './conversation-agent';
export declare function generateMessage(db: DataSource, input: GenerateMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;
export declare function processUserMessage(db: DataSource, params: ProcessUserMessageInput, callbacks?: AgentRuntimeCallbacks): Promise<string>;

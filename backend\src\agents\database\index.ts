import { DataSource } from 'typeorm';

// Import all entities
import { User } from '../../users/user.entity';
import { Store } from '../../stores/store.entity';
import { Customer } from '../../customers/customer.entity';
import { Product } from '../../products/product.entity';
import { Order } from '../../orders/order.entity';
import { OrderItem } from '../../orders/order-item.entity';
import { Conversation } from '../../conversations/conversation.entity';
import { Message } from '../../conversations/message.entity';
import { ToolCall } from '../../tool-calls/tool-call.entity';
import { Agent } from '../agent.entity';
import { Account } from '../../auth/account.entity';
import { Session } from '../../auth/session.entity';
import { VerificationToken } from '../../auth/verification-token.entity';

let dataSource: DataSource | null = null;

export async function getDatabase(): Promise<DataSource> {
  if (dataSource && dataSource.isInitialized) {
    return dataSource;
  }

  // Create a new DataSource instance
  dataSource = new DataSource({
    type: 'postgres',
    host: process.env.DB_HOST || 'localhost',
    port: parseInt(process.env.DB_PORT || '5432'),
    username: process.env.DB_USER || 'postgres',
    password: process.env.DB_PASSWORD || '',
    database: process.env.DB_NAME || 'teno_store',
    synchronize: false,
    logging: false,
    entities: [
      User,
      Store,
      Customer,
      Product,
      Order,
      OrderItem,
      Conversation,
      Message,
      ToolCall,
      Agent,
      Account,
      Session,
      VerificationToken,
    ],
    ssl: process.env.NODE_ENV === 'production' ? { rejectUnauthorized: false } : false,
    extra: {
      max: 20,
      connectionTimeoutMillis: 10000,
      idleTimeoutMillis: 30000,
    },
  });

  if (!dataSource.isInitialized) {
    await dataSource.initialize();
  }

  return dataSource;
}

export async function closeDatabase(): Promise<void> {
  if (dataSource && dataSource.isInitialized) {
    await dataSource.destroy();
    dataSource = null;
  }
}

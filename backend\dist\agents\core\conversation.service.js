"use strict";
Object.defineProperty(exports, "__esModule", { value: true });
exports.generateMessage = generateMessage;
exports.processUserMessage = processUserMessage;
const conversation_agent_1 = require("./conversation-agent");
const dist_1 = require("../../../../ai-agent/dist");
const conversation_entity_1 = require("../../conversations/conversation.entity");
const store_entity_1 = require("../../stores/store.entity");
const message_entity_1 = require("../../conversations/message.entity");
const product_entity_1 = require("../../products/product.entity");
const tool_call_entity_1 = require("../../tool-calls/tool-call.entity");
async function getConversationByUuid(db, conversationUuid) {
    const conversationRepository = db.getRepository(conversation_entity_1.Conversation);
    return conversationRepository.findOne({
        where: { uuid: conversationUuid, isDeleted: false },
        select: ['id', 'storeId'],
    });
}
async function getStoreCurrency(db, storeId) {
    const storeRepository = db.getRepository(store_entity_1.Store);
    const store = await storeRepository.findOne({
        where: { id: storeId.toString(), isDeleted: false },
        select: ['currency'],
    });
    return store?.currency || 'USD';
}
async function getStorePreferredLanguage(db, storeId) {
    const storeRepository = db.getRepository(store_entity_1.Store);
    const store = await storeRepository.findOne({
        where: { id: storeId.toString(), isDeleted: false },
        select: ['preferredLanguage'],
    });
    return store?.preferredLanguage || 'en';
}
function mapHistoryToAgentMessages(historyRecords) {
    return historyRecords.map((m) => ({
        role: m.agentId ? 'assistant' : 'user',
        content: m.content,
    }));
}
function extractMetricsFromResponse(response) {
    return {
        totalCost: response.totalCost || 0,
        totalInputTokens: response.totalInputTokens || 0,
        totalOutputTokens: response.totalOutputTokens || 0,
        totalExecutionTime: response.totalExecutionTime || 0,
    };
}
function convertDecimalToNumber(value) {
    if (value === null || value === undefined)
        return null;
    return Number(value);
}
async function updateConversationTotals(db, conversationId) {
    try {
        const messageRepository = db.getRepository(message_entity_1.Message);
        const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
        const conversationRepository = db.getRepository(conversation_entity_1.Conversation);
        const [messages, toolCalls] = await Promise.all([
            messageRepository.find({
                where: { conversationId: conversationId.toString(), isDeleted: false },
                select: ['id'],
            }),
            toolCallRepository.find({
                where: { conversationId: conversationId.toString(), isDeleted: false },
                select: ['id', 'duration'],
            }),
        ]);
        const totalCost = 0;
        const totalExecutionTime = toolCalls.reduce((sum, item) => sum + (item.duration || 0), 0);
        const totalInputTokens = 0;
        const totalOutputTokens = 0;
        await conversationRepository.update({ id: conversationId.toString() }, {
            updatedAt: new Date(),
        });
    }
    catch (error) {
        console.warn('Failed to update conversation totals:', error);
    }
}
async function persistAgentResponse(params) {
    const { db, agentId, ownerUserId, conversationId, response, toolCalls, toolOutputs, executionDetails, llmCosts } = params;
    const messageRepository = db.getRepository(message_entity_1.Message);
    const toolCallRepository = db.getRepository(tool_call_entity_1.ToolCall);
    const [lastMessage, lastToolCall] = await Promise.all([
        messageRepository.findOne({
            where: { conversationId: conversationId.toString(), isDeleted: false },
            order: { createdAt: 'DESC' },
            select: ['id', 'createdAt'],
        }),
        toolCallRepository.findOne({
            where: { conversationId: conversationId.toString(), isDeleted: false },
            order: { createdAt: 'DESC' },
            select: ['id', 'createdAt'],
        }),
    ]);
    const nextSequence = Date.now();
    if (toolCalls && toolCalls.length > 0) {
        for (let i = 0; i < toolCalls.length; i++) {
            const toolCall = toolCalls[i];
            const toolOutput = toolOutputs?.find(to => to.tool_call_id === toolCall.id);
            const executionDetail = executionDetails?.find(ed => ed.toolCallId === toolCall.id);
            let toolInput = {};
            try {
                toolInput = JSON.parse(toolCall.function.arguments);
            }
            catch (e) {
                toolInput = { raw: toolCall.function.arguments };
            }
            let toolOutputData = undefined;
            if (toolOutput?.content) {
                try {
                    toolOutputData = JSON.parse(toolOutput.content);
                }
                catch (e) {
                    toolOutputData = { rawContent: toolOutput.content };
                }
            }
            try {
                await toolCallRepository.save({
                    toolName: toolCall.function.name,
                    toolInput,
                    conversationId: conversationId.toString(),
                    createdBy: (0, conversation_agent_1.toBigInt)(ownerUserId).toString(),
                    toolOutput: toolOutputData,
                    executionTime: executionDetail?.executionTime,
                    success: executionDetail?.success ?? true,
                    errorMessage: executionDetail?.errorMessage,
                    cost: executionDetail?.cost,
                    inputTokens: executionDetail?.inputTokens,
                    outputTokens: executionDetail?.outputTokens,
                    sequence: nextSequence + i,
                });
            }
            catch (toolCallError) {
                console.warn('Failed to create tool call record:', toolCallError);
            }
        }
    }
    try {
        const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
        const created = await messageRepository.save({
            content: response,
            role: 'assistant',
            createdBy: (0, conversation_agent_1.toBigInt)(ownerUserId).toString(),
            conversationId: conversationId.toString(),
            userId: (0, conversation_agent_1.toBigInt)(ownerUserId).toString(),
            sequence: messageSequence,
            cost: llmCosts?.totalCost,
            executionTime: llmCosts?.totalExecutionTime,
            inputTokens: llmCosts?.totalInputTokens,
            outputTokens: llmCosts?.totalOutputTokens,
        });
        await updateConversationTotals(db, conversationId);
        return created;
    }
    catch (error) {
        console.error('Failed to persist agent response:', error);
        try {
            const messageSequence = toolCalls && toolCalls.length > 0 ? nextSequence + toolCalls.length : nextSequence;
            const fallbackMessage = await messageRepository.save({
                content: response || 'I apologize, but I encountered an error while processing your request.',
                role: 'assistant',
                createdBy: (0, conversation_agent_1.toBigInt)(ownerUserId).toString(),
                conversationId: conversationId.toString(),
                userId: (0, conversation_agent_1.toBigInt)(ownerUserId).toString(),
                sequence: messageSequence,
                cost: llmCosts?.totalCost,
                executionTime: llmCosts?.totalExecutionTime,
                inputTokens: llmCosts?.totalInputTokens,
                outputTokens: llmCosts?.totalOutputTokens,
            });
            await updateConversationTotals(db, conversationId);
            return fallbackMessage;
        }
        catch (fallbackError) {
            console.error('Fallback message creation also failed:', fallbackError);
            throw new Error(`Failed to save agent response: ${fallbackError instanceof Error ? fallbackError.message : String(fallbackError)}`);
        }
    }
}
async function updateNotificationSafely(db, conversationUuid, notification, callbacks) {
    try {
        const conversationRepository = db.getRepository(conversation_entity_1.Conversation);
        await conversationRepository.update({ uuid: conversationUuid }, { updatedAt: new Date() });
        await callbacks?.setNotification?.(conversationUuid, notification);
    }
    catch (error) {
        console.warn(`Failed to update notification status to ${notification}:`, error);
    }
}
async function getConversationAndStoreInfo(db, conversationUuid) {
    const conversation = await getConversationByUuid(db, conversationUuid);
    if (!conversation) {
        throw new Error(`Conversation with UUID ${conversationUuid} not found`);
    }
    const storeCurrency = await getStoreCurrency(db, BigInt(conversation.storeId));
    const storePreferredLanguage = await getStorePreferredLanguage(db, BigInt(conversation.storeId));
    return {
        conversation,
        storeCurrency,
        storePreferredLanguage
    };
}
async function getConversationHistory(db, conversationId) {
    const messageRepository = db.getRepository(message_entity_1.Message);
    const messageHistory = await messageRepository.find({
        where: { conversationId: conversationId.toString(), isDeleted: false },
        order: { createdAt: 'ASC' },
        select: ['content', 'role', 'userId'],
    });
    return messageHistory.map(msg => ({
        role: msg.role,
        content: msg.content
    }));
}
async function getStoreProducts(db, storeId) {
    const productRepository = db.getRepository(product_entity_1.Product);
    const products = await productRepository.find({
        where: {
            storeId: storeId.toString(),
            isDeleted: false
        },
        select: ['id', 'name', 'description', 'price', 'sku'],
        order: { createdAt: 'DESC' },
        take: 50
    });
    return products.map(product => ({
        id: BigInt(product.id),
        name: product.name,
        description: product.description,
        price: Number(product.price),
        sku: product.sku
    }));
}
async function getConversationCustomer(db, conversationUuid) {
    return null;
}
async function generateMessage(db, input, callbacks) {
    await (0, conversation_agent_1.ensureLLMProviderConfigured)();
    const startTime = Date.now();
    const { conversation, storeCurrency, storePreferredLanguage } = await getConversationAndStoreInfo(db, input.conversationUuid);
    const products = await getStoreProducts(db, BigInt(conversation.storeId));
    const customer = await getConversationCustomer(db, input.conversationUuid);
    const agent = (0, conversation_agent_1.buildConversationAgent)(db, input.conversationUuid, storeCurrency, storePreferredLanguage);
    const mapped = await getConversationHistory(db, BigInt(conversation.id));
    const productContextMessage = (0, conversation_agent_1.buildProductContextMessage)(products, storeCurrency);
    const customerContextMessage = (0, conversation_agent_1.buildCustomerContextMessage)(customer);
    const allMessages = [];
    if (productContextMessage)
        allMessages.push(productContextMessage);
    if (customerContextMessage)
        allMessages.push(customerContextMessage);
    allMessages.push(...mapped);
    allMessages.push({ role: 'user', content: 'Continue the conversation.' });
    const response = await dist_1.LlmApi.generateLlmResponse(agent, allMessages);
    const content = response.content;
    const executionTime = Date.now() - startTime;
    const metrics = extractMetricsFromResponse(response);
    await persistAgentResponse({
        db,
        agentId: input.agentId,
        ownerUserId: input.ownerUserId,
        conversationId: BigInt(conversation.id),
        response: content,
        toolCalls: response.calls,
        toolOutputs: response.outputs,
        executionDetails: response.executionDetails,
        llmCosts: {
            ...metrics,
            totalExecutionTime: executionTime,
        },
    });
    return content;
}
async function processUserMessage(db, params, callbacks) {
    console.log(`🔧 processUserMessage called with:`, {
        dbType: typeof db,
        dbExists: !!db,
        dbInitialized: db?.isInitialized,
        params,
        timestamp: new Date().toISOString()
    });
    await (0, conversation_agent_1.ensureLLMProviderConfigured)();
    const { conversationUuid, userMessage, ownerUserId, agentId } = params;
    const startTime = Date.now();
    try {
        if (!db || !db.isInitialized) {
            console.error(`❌ Database connection validation failed in processUserMessage:`, {
                dbExists: !!db,
                dbInitialized: db?.isInitialized,
                dbType: typeof db
            });
            throw new Error('Database connection is not properly initialized in processUserMessage');
        }
        console.log(`✅ Database connection validated in processUserMessage`);
        await updateNotificationSafely(db, conversationUuid, conversation_agent_1.ConversationNotification.AgentIsThinking, callbacks);
        const { conversation, storeCurrency, storePreferredLanguage } = await getConversationAndStoreInfo(db, conversationUuid);
        const products = await getStoreProducts(db, BigInt(conversation.storeId));
        const customer = null;
        console.log(`🔧 Building conversation agent with database connection:`, {
            dbType: typeof db,
            dbInitialized: db?.isInitialized,
            conversationUuid,
            storeCurrency,
            storePreferredLanguage
        });
        const conversationAgent = (0, conversation_agent_1.buildConversationAgent)(db, conversationUuid, storeCurrency, storePreferredLanguage);
        const history = await getConversationHistory(db, BigInt(conversation.id));
        const productContextMessage = (0, conversation_agent_1.buildProductContextMessage)(products, storeCurrency);
        const customerContextMessage = (0, conversation_agent_1.buildCustomerContextMessage)(customer);
        const allMessages = [];
        if (productContextMessage)
            allMessages.push(productContextMessage);
        if (customerContextMessage)
            allMessages.push(customerContextMessage);
        allMessages.push(...history);
        allMessages.push({ role: 'user', content: userMessage });
        await updateNotificationSafely(db, conversationUuid, conversation_agent_1.ConversationNotification.AgentIsGeneratingResponse, callbacks);
        const response = await dist_1.LlmApi.generateLlmResponse(conversationAgent, allMessages);
        const sanitizedResponse = response.content;
        const executionTime = Date.now() - startTime;
        const metrics = extractMetricsFromResponse(response);
        const created = await persistAgentResponse({
            db,
            agentId,
            ownerUserId,
            conversationId: BigInt(conversation.id),
            response: sanitizedResponse,
            toolCalls: response.calls,
            toolOutputs: response.outputs,
            executionDetails: response.executionDetails,
            llmCosts: {
                ...metrics,
                totalExecutionTime: executionTime,
            },
        });
        await updateNotificationSafely(db, conversationUuid, conversation_agent_1.ConversationNotification.None, callbacks);
        return created.content;
    }
    catch (error) {
        await updateNotificationSafely(db, conversationUuid, conversation_agent_1.ConversationNotification.None, callbacks);
        throw error;
    }
}
//# sourceMappingURL=conversation.service.js.map
import { DataSource } from 'typeorm';
import { ToolCall } from '../../tool-calls/tool-call.entity';
import { Message } from '../../conversations/message.entity';
import { Conversation } from '../../conversations/conversation.entity';

export interface CreateToolCallInput {
	toolName: string;
	toolInput: any;
	conversationId: bigint;
	createdBy: bigint;
	toolOutput?: any;
	executionTime?: number;
	success?: boolean;
	errorMessage?: string;
	cost?: number;
	inputTokens?: number;
	outputTokens?: number;
}

export interface UpdateToolCallInput {
	id: bigint;
	toolOutput?: any;
	executionTime?: number;
	success?: boolean;
	errorMessage?: string;
	cost?: number;
	inputTokens?: number;
	outputTokens?: number;
	updatedBy: bigint;
}

export interface ToolCallFilter {
	conversationId?: bigint;
	toolName?: string;
	success?: boolean;
	createdAfter?: Date;
	createdBefore?: Date;
}

/**
 * Create a new tool call record
 */
export async function createToolCall(
	db: DataSource,
	input: CreateToolCallInput
): Promise<any> {
	const { toolName, toolInput, conversationId, createdBy, toolOutput, executionTime, success = true, errorMessage, cost, inputTokens, outputTokens } = input;

	try {
		const messageRepository = db.getRepository(Message);
		const toolCallRepository = db.getRepository(ToolCall);
		const conversationRepository = db.getRepository(Conversation);

		// Get the next unified sequence number for this conversation (using createdAt as ordering)
		const [lastMessage, lastToolCall] = await Promise.all([
			messageRepository.findOne({
				where: { conversationId: conversationId.toString(), isDeleted: false },
				order: { createdAt: 'DESC' },
				select: ['id', 'createdAt'],
			}),
			toolCallRepository.findOne({
				where: { conversationId: conversationId.toString(), isDeleted: false },
				order: { createdAt: 'DESC' },
				select: ['id', 'createdAt'],
			}),
		]);

		// Use timestamp-based ordering instead of sequence
		const nextSequence = Date.now();

		const toolCall = await toolCallRepository.save({
			toolName,
			parameters: toolInput,
			conversationId: conversationId.toString(),
			createdBy: createdBy.toString(),
			result: toolOutput,
			duration: executionTime || 0,
			status: success ? 'completed' : 'failed',
			error: errorMessage,
			userId: createdBy.toString(),
		});

		// Update conversation totals after creating tool call
		await conversationRepository.update(
			{ id: conversationId.toString() },
			{
				updatedAt: new Date(),
			}
		);

		return toolCall;
	} catch (error) {
		console.error('Failed to create tool call record:', error);
		throw new Error(`Failed to create tool call record: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Update an existing tool call record
 */
export async function updateToolCall(
	db: DataSource,
	input: UpdateToolCallInput
): Promise<any> {
	const { id, toolOutput, executionTime, success, errorMessage, updatedBy } = input;

	try {
		const toolCallRepository = db.getRepository(ToolCall);

		const toolCall = await toolCallRepository.update(
			{
				id: id.toString(),
				isDeleted: false,
			},
			{
				result: toolOutput,
				duration: executionTime || 0,
				status: success ? 'completed' : 'failed',
				error: errorMessage,
				updatedBy: updatedBy.toString(),
				updatedAt: new Date(),
			}
		);

		return toolCall;
	} catch (error) {
		console.error('Failed to update tool call record:', error);
		throw new Error(`Failed to update tool call record: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Get tool calls for a conversation
 */
export async function getToolCallsByConversation(
	db: DataSource,
	conversationId: bigint,
	limit?: number,
	offset?: number
): Promise<any[]> {
	try {
		const toolCallRepository = db.getRepository(ToolCall);
		
		const toolCalls = await toolCallRepository.find({
			where: {
				conversationId: conversationId.toString(),
				isDeleted: false,
			},
			order: {
				createdAt: 'ASC',
			},
			take: limit,
			skip: offset,
		});

		return toolCalls;
	} catch (error) {
		console.error('Failed to get tool calls for conversation:', error);
		throw new Error(`Failed to get tool calls for conversation: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Get tool calls with filtering
 */
export async function getToolCalls(
	db: DataSource,
	filter: ToolCallFilter,
	limit?: number,
	offset?: number
): Promise<any[]> {
	try {
		const toolCallRepository = db.getRepository(ToolCall);
		
		const where: any = {
			isDeleted: false,
		};

		if (filter.conversationId) {
			where.conversationId = filter.conversationId;
		}

		if (filter.toolName) {
			where.toolName = filter.toolName;
		}

		if (filter.success !== undefined) {
			where.success = filter.success;
		}

		if (filter.createdAfter || filter.createdBefore) {
			where.createdAt = {};
			if (filter.createdAfter) {
				where.createdAt.gte = filter.createdAfter;
			}
			if (filter.createdBefore) {
				where.createdAt.lte = filter.createdBefore;
			}
		}

		const toolCalls = await toolCallRepository.find({
			where,
			order: {
				createdAt: 'DESC',
			},
			take: limit,
			skip: offset,
		});

		return toolCalls;
	} catch (error) {
		console.error('Failed to get tool calls:', error);
		throw new Error(`Failed to get tool calls: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Get tool call by ID
 */
export async function getToolCallById(
	db: DataSource,
	id: bigint
): Promise<any | null> {
	try {
		const toolCallRepository = db.getRepository(ToolCall);
		
		const toolCall = await toolCallRepository.findOne({
			where: {
				id: id.toString(),
				isDeleted: false,
			},
		});

		return toolCall;
	} catch (error) {
		console.error('Failed to get tool call by ID:', error);
		throw new Error(`Failed to get tool call by ID: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Delete tool call (soft delete)
 */
export async function deleteToolCall(
	db: DataSource,
	id: bigint,
	deletedBy: bigint
): Promise<boolean> {
	try {
		const toolCallRepository = db.getRepository(ToolCall);
		
		await toolCallRepository.update(
			{
				id: id.toString(),
				isDeleted: false,
			},
			{
				isDeleted: true,
				updatedBy: deletedBy.toString(),
				updatedAt: new Date(),
			}
		);

		return true;
	} catch (error) {
		console.error('Failed to delete tool call:', error);
		throw new Error(`Failed to delete tool call: ${error instanceof Error ? error.message : String(error)}`);
	}
}

/**
 * Get tool call statistics for a conversation
 */
export async function getToolCallStats(
	db: DataSource,
	conversationId: bigint
): Promise<{
	total: number;
	successful: number;
	failed: number;
	averageExecutionTime: number | null;
	toolUsage: Record<string, number>;
}> {
	try {
		const toolCallRepository = db.getRepository(ToolCall);
		
		const toolCalls = await toolCallRepository.find({
			where: {
				conversationId: conversationId.toString(),
				isDeleted: false,
			},
			select: ['toolName', 'status', 'duration'],
		});

		const total = toolCalls.length;
		  const successful = toolCalls.filter((tc: any) => tc.success).length;
  const failed = total - successful;

  const executionTimes = toolCalls
    .filter((tc: any) => tc.executionTime !== null)
    .map((tc: any) => tc.executionTime!);

  const averageExecutionTime = executionTimes.length > 0
    ? executionTimes.reduce((sum: number, time: number) => sum + time, 0) / executionTimes.length
    : null;

  const toolUsage: Record<string, number> = {};
  toolCalls.forEach((tc: any) => {
    toolUsage[tc.toolName] = (toolUsage[tc.toolName] || 0) + 1;
  });

		return {
			total,
			successful,
			failed,
			averageExecutionTime,
			toolUsage,
		};
	} catch (error) {
		console.error('Failed to get tool call stats:', error);
		throw new Error(`Failed to get tool call stats: ${error instanceof Error ? error.message : String(error)}`);
	}
}
